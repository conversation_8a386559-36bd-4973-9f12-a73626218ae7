#!/usr/bin/env python3
"""
A minimal, memory-efficient Telegram bot that adds IPs to a security group.
"""

import logging
import logging.handlers
import os
import signal
import sys
from typing import Iterable

import telebot
from telebot.types import Message
from telebot.util import antiflood

# -----------------------------------------------------------------------------
# Configuration
# -----------------------------------------------------------------------------
# Prefer env var; avoid hard-coding secrets. Falls back to existing token only if env missing.
TOKEN = os.getenv("TELEGRAM_BOT_TOKEN") or "**********************************************"
if not TOKEN:
    sys.exit("TELEGRAM_BOT_TOKEN environment variable is required")

LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO").upper()
LOG_FILE = os.getenv("LOG_FILE", "telegram_bot.log")
# Reduce log sizes for lower disk and memory pressure
MAX_LOG_BYTES = int(os.getenv("MAX_LOG_BYTES", "200000"))  # 200 KB
BACKUP_COUNT = int(os.getenv("BACKUP_COUNT", "2"))

# Polling tune
POLL_TIMEOUT = int(os.getenv("POLL_TIMEOUT", "30"))  # seconds
LONG_POLL_TIMEOUT = int(os.getenv("LONG_POLL_TIMEOUT", "25"))
ALLOWED_UPDATES = ("message",)  # limit to essential updates to reduce payloads

# -----------------------------------------------------------------------------
# Logging setup (with rotation)
# -----------------------------------------------------------------------------
# Use a simpler formatter to reduce per-record formatting overhead
_formatter = logging.Formatter("%(asctime)s %(levelname)s %(message)s")
_stdout_handler = logging.StreamHandler(sys.stdout)
_stdout_handler.setFormatter(_formatter)

_file_handler = logging.handlers.RotatingFileHandler(
    LOG_FILE, maxBytes=MAX_LOG_BYTES, backupCount=BACKUP_COUNT
)
_file_handler.setFormatter(_formatter)

root_logger = logging.getLogger()
root_logger.setLevel(LOG_LEVEL)
# Clear pre-existing handlers to avoid duplicates
root_logger.handlers.clear()
root_logger.addHandler(_stdout_handler)
root_logger.addHandler(_file_handler)

logger = logging.getLogger(__name__)
logger.info("EduBot initializing")

# -----------------------------------------------------------------------------
# Bot initialization
# -----------------------------------------------------------------------------
# Bot will be created lazily in __main__
bot: telebot.TeleBot | None = None

# -----------------------------------------------------------------------------
# Message templates
# -----------------------------------------------------------------------------
HELP_TEXT = (
    "Here are some commands that you can use:\n"
    "/add &lt;IP address&gt; – add the IP to the Dynamic Security Group"
)
UNKNOWN_COMMAND = "Unknown command. Use /help to see available commands."
IP_ADDED_TEMPLATE = "✅ IP <code>{ip}</code> added to Security Group."

# -----------------------------------------------------------------------------
# Handlers
# -----------------------------------------------------------------------------
def _iter_args(text: str) -> Iterable[str]:
    # Generator-style iteration to avoid temporary list retention
    for part in text.strip().split():
        if part:
            yield part

def _first_arg_after_command(text: str) -> str | None:
    it = _iter_args(text)
    try:
        next(it)  # skip command itself
    except StopIteration:
        return None
    return next(it, None)

def handle_add_command(message: Message) -> None:
    """Handle /add <ip>."""
    ip = _first_arg_after_command(message.text or "")
    if not ip:
        _safe_reply(message, USAGE_ADD)
        return

    # TODO: integrate real AWS security-group logic here
    _safe_reply(message, IP_ADDED_TEMPLATE.format(ip=ip))
    logger.info("User %s added IP %s", getattr(message.from_user, "id", "unknown"), ip)

def handle_help_command(message: Message) -> None:
    """Handle /help."""
    _safe_reply(message, HELP_TEXT)

def handle_unknown(message: Message) -> None:
    """Handle any other text."""
    _safe_reply(message, UNKNOWN_COMMAND)

# -----------------------------------------------------------------------------
# Routing
# -----------------------------------------------------------------------------
def register_handlers(_bot: telebot.TeleBot) -> None:
    _bot.register_message_handler(handle_add_command, commands=["add"], pass_bot=False)
    _bot.register_message_handler(handle_help_command, commands=["help"], pass_bot=False)

    # Use a stable predicate instead of lambda to avoid closure allocations
    def _always_true(_msg):
        return True
    _bot.register_message_handler(handle_unknown, func=_always_true, pass_bot=False)
    # Limit updates handled to reduce parsing work
    try:
        _bot.set_my_commands([telebot.types.BotCommand("add", "Add IP"), telebot.types.BotCommand("help", "Help")])
    except Exception:
        # Best-effort; older bot API versions may not support or may fail without rights
        pass

# -----------------------------------------------------------------------------
# Graceful shutdown
# -----------------------------------------------------------------------------
def _install_signals(_bot: telebot.TeleBot) -> None:
    def shutdown(signum, frame) -> None:  # noqa: D401
        """Stop the bot on SIGINT/SIGTERM."""
        logger.info("Received signal %s, shutting down gracefully...", signum)
        try:
            _bot.stop_bot()
        finally:
            for h in list(logging.getLogger().handlers):
                try:
                    h.flush()
                except Exception:
                    pass
        sys.exit(0)

    signal.signal(signal.SIGINT, shutdown)
    signal.signal(signal.SIGTERM, shutdown)

# Safe reply with anti-flood protection
def _safe_reply(message: Message, text: str) -> None:
    try:
        # Use module-level bot ref; send minimal payload with reply_to id
        antiflood(bot.send_message, message.chat.id, text, reply_to_message_id=message.message_id)
    except Exception as e:
        logging.getLogger(__name__).warning("Reply failed: %s", e)

# -----------------------------------------------------------------------------
# Entrypoint
# -----------------------------------------------------------------------------
if __name__ == "__main__":
    # Lazy creation of bot with low-resource settings
    bot = telebot.TeleBot(
        TOKEN,
        parse_mode="HTML",
        threaded=False,         # single-threaded handlers
        num_threads=1,          # ensure minimal workers
        skip_pending=True,      # drop backlog on start
        disable_web_page_preview=True,  # smaller messages
    )
    register_handlers(bot)
    _install_signals(bot)
    logger.info("Bot is polling…")
    # Use allowed_updates to reduce payload and parsing; keep timeouts modest
    try:
        bot.infinity_polling(
            timeout=POLL_TIMEOUT,
            long_polling_timeout=LONG_POLL_TIMEOUT,
            allowed_updates=list(ALLOWED_UPDATES),
            restart_on_change=False,  # avoid file watcher overhead
            logger_level=logging.ERROR,  # reduce internal noise
        )
    except KeyboardInterrupt:
        pass
    finally:
        try:
            bot.stop_bot()
        except Exception:
            pass
        # Flush handlers explicitly
        for h in list(logging.getLogger().handlers):
            try:
                h.flush()
            except Exception:
                pass