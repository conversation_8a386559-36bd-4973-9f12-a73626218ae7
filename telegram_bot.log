2025-07-30 22:18:05- INFO - EduBot has started
2025-07-30 22:25:25,052 - INFO - EduBot has started
2025-07-30 22:25:25,052 - <PERSON>F<PERSON> - <PERSON><PERSON> is polling…
2025-07-30 22:25:37,190 - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Unsupported start tag "ip" at byte offset 46
2025-07-30 22:25:37,196 - ERROR - Exception traceback:
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1235, in __threaded_polling
    self.worker_pool.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/util.py", line 151, in raise_exceptions
    raise self.exception_info
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/util.py", line 94, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 9854, in _run_middlewares_and_handler
    result = handler['function'](message)
  File "/Users/<USER>/Documents/Progi/Python/pythro/pythro.py", line 80, in handle_help_command
    bot.reply_to(message, HELP_TEXT)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 6067, in reply_to
    return self.send_message(message.chat.id, text, reply_parameters=reply_parameters, **kwargs)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1800, in send_message
    apihelper.send_message(
    ~~~~~~~~~~~~~~~~~~~~~~^
        self.token, chat_id, text,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        reply_parameters=reply_parameters, link_preview_options=link_preview_options, business_connection_id=business_connection_id,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        message_effect_id=message_effect_id, allow_paid_broadcast=allow_paid_broadcast))
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 275, in send_message
    return _make_request(token, method_url, params=payload, method='post')
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 168, in _make_request
    json_result = _check_result(method_name, result)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 195, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Unsupported start tag "ip" at byte offset 46

2025-07-30 22:28:04,917 - INFO - Received signal 2, shutting down gracefully...
2025-07-30 22:28:05,142 - INFO - Received signal 2, shutting down gracefully...
2025-07-30 22:28:08,576 - INFO - EduBot has started
2025-07-30 22:28:08,576 - INFO - Bot is polling…
2025-07-30 22:28:24,717 - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Unsupported start tag "ip" at byte offset 46
2025-07-30 22:28:24,719 - ERROR - Exception traceback:
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1235, in __threaded_polling
    self.worker_pool.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/util.py", line 151, in raise_exceptions
    raise self.exception_info
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/util.py", line 94, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 9854, in _run_middlewares_and_handler
    result = handler['function'](message)
  File "/Users/<USER>/Documents/Progi/Python/pythro/pythro.py", line 80, in handle_help_command
    bot.reply_to(message, HELP_TEXT)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 6067, in reply_to
    return self.send_message(message.chat.id, text, reply_parameters=reply_parameters, **kwargs)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1800, in send_message
    apihelper.send_message(
    ~~~~~~~~~~~~~~~~~~~~~~^
        self.token, chat_id, text,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        reply_parameters=reply_parameters, link_preview_options=link_preview_options, business_connection_id=business_connection_id,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        message_effect_id=message_effect_id, allow_paid_broadcast=allow_paid_broadcast))
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 275, in send_message
    return _make_request(token, method_url, params=payload, method='post')
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 168, in _make_request
    json_result = _check_result(method_name, result)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 195, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Unsupported start tag "ip" at byte offset 46

2025-07-30 22:35:55,668 - INFO - EduBot has started
2025-07-30 22:35:55,669 - INFO - Bot is polling…
2025-07-30 22:36:40,943 - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Unsupported start tag "ip" at byte offset 46
2025-07-30 22:36:40,945 - ERROR - Exception traceback:
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1235, in __threaded_polling
    self.worker_pool.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/util.py", line 151, in raise_exceptions
    raise self.exception_info
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/util.py", line 94, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 9854, in _run_middlewares_and_handler
    result = handler['function'](message)
  File "/Users/<USER>/Documents/Progi/Python/pythro/pythro.py", line 80, in handle_help_command
    bot.reply_to(message, HELP_TEXT)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 6067, in reply_to
    return self.send_message(message.chat.id, text, reply_parameters=reply_parameters, **kwargs)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1800, in send_message
    apihelper.send_message(
    ~~~~~~~~~~~~~~~~~~~~~~^
        self.token, chat_id, text,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        reply_parameters=reply_parameters, link_preview_options=link_preview_options, business_connection_id=business_connection_id,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        message_effect_id=message_effect_id, allow_paid_broadcast=allow_paid_broadcast))
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 275, in send_message
    return _make_request(token, method_url, params=payload, method='post')
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 168, in _make_request
    json_result = _check_result(method_name, result)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 195, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Unsupported start tag "ip" at byte offset 46

2025-07-30 22:37:49,958 - INFO - Received signal 2, shutting down gracefully...
2025-07-30 22:38:45,224 - INFO - EduBot has started
2025-07-30 22:38:45,224 - INFO - Bot is polling…
2025-07-30 22:38:58,255 - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Unsupported start tag "ip" at byte offset 46
2025-07-30 22:38:58,257 - ERROR - Exception traceback:
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1235, in __threaded_polling
    self.worker_pool.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/util.py", line 151, in raise_exceptions
    raise self.exception_info
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/util.py", line 94, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 9854, in _run_middlewares_and_handler
    result = handler['function'](message)
  File "/Users/<USER>/Documents/Progi/Python/pythro/pythro.py", line 80, in handle_help_command
    bot.reply_to(message, HELP_TEXT)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 6067, in reply_to
    return self.send_message(message.chat.id, text, reply_parameters=reply_parameters, **kwargs)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1800, in send_message
    apihelper.send_message(
    ~~~~~~~~~~~~~~~~~~~~~~^
        self.token, chat_id, text,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        reply_parameters=reply_parameters, link_preview_options=link_preview_options, business_connection_id=business_connection_id,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        message_effect_id=message_effect_id, allow_paid_broadcast=allow_paid_broadcast))
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 275, in send_message
    return _make_request(token, method_url, params=payload, method='post')
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 168, in _make_request
    json_result = _check_result(method_name, result)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 195, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Unsupported start tag "ip" at byte offset 46

2025-07-30 22:39:48,289 - INFO - Received signal 2, shutting down gracefully...
2025-07-30 22:39:51,455 - INFO - EduBot has started
2025-07-30 22:39:51,455 - INFO - Bot is polling…
2025-07-30 22:39:59,578 - ERROR - Threaded polling exception: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Unsupported start tag "ip" at byte offset 46
2025-07-30 22:39:59,580 - ERROR - Exception traceback:
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1235, in __threaded_polling
    self.worker_pool.raise_exceptions()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/util.py", line 151, in raise_exceptions
    raise self.exception_info
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/util.py", line 94, in run
    task(*args, **kwargs)
    ~~~~^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 9854, in _run_middlewares_and_handler
    result = handler['function'](message)
  File "/Users/<USER>/Documents/Progi/Python/pythro/pythro.py", line 80, in handle_help_command
    bot.reply_to(message, HELP_TEXT)
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 6067, in reply_to
    return self.send_message(message.chat.id, text, reply_parameters=reply_parameters, **kwargs)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1800, in send_message
    apihelper.send_message(
    ~~~~~~~~~~~~~~~~~~~~~~^
        self.token, chat_id, text,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^
    ...<2 lines>...
        reply_parameters=reply_parameters, link_preview_options=link_preview_options, business_connection_id=business_connection_id,
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
        message_effect_id=message_effect_id, allow_paid_broadcast=allow_paid_broadcast))
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 275, in send_message
    return _make_request(token, method_url, params=payload, method='post')
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 168, in _make_request
    json_result = _check_result(method_name, result)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 195, in _check_result
    raise ApiTelegramException(method_name, result, result_json)
telebot.apihelper.ApiTelegramException: A request to the Telegram API was unsuccessful. Error code: 400. Description: Bad Request: can't parse entities: Unsupported start tag "ip" at byte offset 46

2025-07-30 22:40:57,544 - INFO - Received signal 2, shutting down gracefully...
2025-07-30 22:41:00,483 - INFO - EduBot has started
2025-07-30 22:41:00,483 - INFO - Bot is polling…
2025-07-30 22:41:23,498 - INFO - User 6190048936 added IP ***********
2025-07-30 22:41:33,623 - INFO - Received signal 2, shutting down gracefully...
2025-07-31 06:26:03,088 INFO EduBot initializing
2025-07-31 06:26:03,232 INFO Bot is polling…
2025-07-31 06:36:31,178 ERROR Infinity polling exception: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)
2025-07-31 06:36:31,194 ERROR Exception traceback:
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
TimeoutError: The read operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/util/retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/util/util.py", line 39, in reraise
    raise value
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1110, in infinity_polling
    self.polling(non_stop=True, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 logger_level=logger_level, allowed_updates=allowed_updates, restart_on_change=False,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 *args, **kwargs)
                 ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1201, in polling
    self.__non_threaded_polling(non_stop=non_stop, interval=interval, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                                logger_level=logger_level, allowed_updates=allowed_updates)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1327, in __non_threaded_polling
    raise e
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1299, in __non_threaded_polling
    self.__retrieve_updates(timeout, long_polling_timeout, allowed_updates=allowed_updates)
    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 688, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 660, in get_updates
    json_updates = apihelper.get_updates(
        self.token, offset=offset, limit=limit, timeout=timeout, allowed_updates=allowed_updates,
        long_polling_timeout=long_polling_timeout)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 332, in get_updates
    return _make_request(token, method_url, params=payload)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 162, in _make_request
    result = _get_req_session().request(
        method, request_url, params=params, files=files,
        timeout=(connect_timeout, read_timeout), proxies=proxy)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)

2025-07-31 07:02:18,577 ERROR Infinity polling exception: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)
2025-07-31 07:02:18,587 ERROR Exception traceback:
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
TimeoutError: The read operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/util/retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/util/util.py", line 39, in reraise
    raise value
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1110, in infinity_polling
    self.polling(non_stop=True, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 logger_level=logger_level, allowed_updates=allowed_updates, restart_on_change=False,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 *args, **kwargs)
                 ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1201, in polling
    self.__non_threaded_polling(non_stop=non_stop, interval=interval, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                                logger_level=logger_level, allowed_updates=allowed_updates)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1327, in __non_threaded_polling
    raise e
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1299, in __non_threaded_polling
    self.__retrieve_updates(timeout, long_polling_timeout, allowed_updates=allowed_updates)
    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 688, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 660, in get_updates
    json_updates = apihelper.get_updates(
        self.token, offset=offset, limit=limit, timeout=timeout, allowed_updates=allowed_updates,
        long_polling_timeout=long_polling_timeout)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 332, in get_updates
    return _make_request(token, method_url, params=payload)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 162, in _make_request
    result = _get_req_session().request(
        method, request_url, params=params, files=files,
        timeout=(connect_timeout, read_timeout), proxies=proxy)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)

2025-07-31 07:47:28,853 ERROR Infinity polling exception: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)
2025-07-31 07:47:28,863 ERROR Exception traceback:
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
TimeoutError: The read operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/util/retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/util/util.py", line 39, in reraise
    raise value
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1110, in infinity_polling
    self.polling(non_stop=True, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 logger_level=logger_level, allowed_updates=allowed_updates, restart_on_change=False,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 *args, **kwargs)
                 ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1201, in polling
    self.__non_threaded_polling(non_stop=non_stop, interval=interval, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                                logger_level=logger_level, allowed_updates=allowed_updates)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1327, in __non_threaded_polling
    raise e
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1299, in __non_threaded_polling
    self.__retrieve_updates(timeout, long_polling_timeout, allowed_updates=allowed_updates)
    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 688, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 660, in get_updates
    json_updates = apihelper.get_updates(
        self.token, offset=offset, limit=limit, timeout=timeout, allowed_updates=allowed_updates,
        long_polling_timeout=long_polling_timeout)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 332, in get_updates
    return _make_request(token, method_url, params=payload)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 162, in _make_request
    result = _get_req_session().request(
        method, request_url, params=params, files=files,
        timeout=(connect_timeout, read_timeout), proxies=proxy)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)

2025-07-31 08:05:28,379 ERROR Infinity polling exception: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)
2025-07-31 08:05:28,389 ERROR Exception traceback:
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
TimeoutError: The read operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/util/retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/util/util.py", line 39, in reraise
    raise value
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1110, in infinity_polling
    self.polling(non_stop=True, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 logger_level=logger_level, allowed_updates=allowed_updates, restart_on_change=False,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 *args, **kwargs)
                 ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1201, in polling
    self.__non_threaded_polling(non_stop=non_stop, interval=interval, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                                logger_level=logger_level, allowed_updates=allowed_updates)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1327, in __non_threaded_polling
    raise e
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1299, in __non_threaded_polling
    self.__retrieve_updates(timeout, long_polling_timeout, allowed_updates=allowed_updates)
    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 688, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 660, in get_updates
    json_updates = apihelper.get_updates(
        self.token, offset=offset, limit=limit, timeout=timeout, allowed_updates=allowed_updates,
        long_polling_timeout=long_polling_timeout)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 332, in get_updates
    return _make_request(token, method_url, params=payload)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 162, in _make_request
    result = _get_req_session().request(
        method, request_url, params=params, files=files,
        timeout=(connect_timeout, read_timeout), proxies=proxy)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)

2025-07-31 08:10:39,872 ERROR Infinity polling exception: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)
2025-07-31 08:10:39,878 ERROR Exception traceback:
Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 534, in _make_request
    response = conn.getresponse()
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connection.py", line 565, in getresponse
    httplib_response = super().getresponse()
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 1430, in getresponse
    response.begin()
    ~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 331, in begin
    version, status, reason = self._read_status()
                              ~~~~~~~~~~~~~~~~~^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/http/client.py", line 292, in _read_status
    line = str(self.fp.readline(_MAXLINE + 1), "iso-8859-1")
               ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/socket.py", line 719, in readinto
    return self._sock.recv_into(b)
           ~~~~~~~~~~~~~~~~~~~~^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ssl.py", line 1304, in recv_into
    return self.read(nbytes, buffer)
           ~~~~~~~~~^^^^^^^^^^^^^^^^
  File "/opt/homebrew/Cellar/python@3.13/3.13.5/Frameworks/Python.framework/Versions/3.13/lib/python3.13/ssl.py", line 1138, in read
    return self._sslobj.read(len, buffer)
           ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
TimeoutError: The read operation timed out

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/adapters.py", line 667, in send
    resp = conn.urlopen(
        method=request.method,
    ...<9 lines>...
        chunked=chunked,
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 841, in urlopen
    retries = retries.increment(
        method, url, error=new_e, _pool=self, _stacktrace=sys.exc_info()[2]
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/util/retry.py", line 474, in increment
    raise reraise(type(error), error, _stacktrace)
          ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/util/util.py", line 39, in reraise
    raise value
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 787, in urlopen
    response = self._make_request(
        conn,
    ...<10 lines>...
        **response_kw,
    )
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 536, in _make_request
    self._raise_timeout(err=e, url=url, timeout_value=read_timeout)
    ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/urllib3/connectionpool.py", line 367, in _raise_timeout
    raise ReadTimeoutError(
        self, url, f"Read timed out. (read timeout={timeout_value})"
    ) from err
urllib3.exceptions.ReadTimeoutError: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1110, in infinity_polling
    self.polling(non_stop=True, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 logger_level=logger_level, allowed_updates=allowed_updates, restart_on_change=False,
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                 *args, **kwargs)
                 ^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1201, in polling
    self.__non_threaded_polling(non_stop=non_stop, interval=interval, timeout=timeout, long_polling_timeout=long_polling_timeout,
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
                                logger_level=logger_level, allowed_updates=allowed_updates)
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1327, in __non_threaded_polling
    raise e
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 1299, in __non_threaded_polling
    self.__retrieve_updates(timeout, long_polling_timeout, allowed_updates=allowed_updates)
    ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 688, in __retrieve_updates
    updates = self.get_updates(offset=(self.last_update_id + 1),
                               allowed_updates=allowed_updates,
                               timeout=timeout, long_polling_timeout=long_polling_timeout)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/__init__.py", line 660, in get_updates
    json_updates = apihelper.get_updates(
        self.token, offset=offset, limit=limit, timeout=timeout, allowed_updates=allowed_updates,
        long_polling_timeout=long_polling_timeout)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 332, in get_updates
    return _make_request(token, method_url, params=payload)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/telebot/apihelper.py", line 162, in _make_request
    result = _get_req_session().request(
        method, request_url, params=params, files=files,
        timeout=(connect_timeout, read_timeout), proxies=proxy)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "/Users/<USER>/Documents/Progi/Python/pythro/.venv/lib/python3.13/site-packages/requests/adapters.py", line 713, in send
    raise ReadTimeout(e, request=request)
requests.exceptions.ReadTimeout: HTTPSConnectionPool(host='api.telegram.org', port=443): Read timed out. (read timeout=30)

2025-07-31 08:24:53,858 INFO Received signal 2, shutting down gracefully...
