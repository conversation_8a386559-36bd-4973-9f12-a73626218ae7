import boto3,time
import os,sys,json
import logging
from botocore.exceptions import ClientError, NoCredentialsError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pthu.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


current_folder = os.path.dirname(os.path.realpath(__file__))
endtime_query = 0
starttime_query = 0

try:
    if os.path.isfile(current_folder+"/.starttime"):
        try:
            with open(current_folder+"/.starttime","r") as stfile:
                starttime_query = str(stfile.read()).strip()
                endtime_query = str(int(time.time()) - 180)
                starttime = endtime_query
            with open(current_folder+"/.starttime","w") as stfile:
                stfile.write(str(starttime))
            logger.info(f"Loaded existing starttime: {starttime_query}")
        except IOError as e:
            logger.error(f"Error reading/writing .starttime file: {e}")
            sys.exit(1)
    else:
        try:
            endtime_query = str(int(time.time()) - 120)
            endtime_query = int(endtime_query[:-2]+'00')
            starttime_query = str(endtime_query - 600)
            starttime = endtime_query
            with open(current_folder+"/.starttime","w") as stfile:
                stfile.write(str(starttime))
            logger.info(f"Created new starttime: {starttime_query}")
        except IOError as e:
            logger.error(f"Error creating .starttime file: {e}")
            sys.exit(1)
except Exception as e:
    logger.error(f"Unexpected error in time configuration: {e}")
    sys.exit(1)


access_key = '********************'
secret_key = 'S8BhHv3W5q/L6XLOERXyBEcU+KW+/vDL/DMML18K'
region = 'eu-central-1'

try:
    client = boto3.client('logs', aws_access_key_id=access_key, aws_secret_access_key=secret_key, region_name=region)
    logger.info("Successfully created AWS CloudWatch Logs client")
except NoCredentialsError as e:
    logger.error(f"AWS credentials error: {e}")
    sys.exit(1)
except Exception as e:
    logger.error(f"Error creating AWS client: {e}")
    sys.exit(1)


try:
    logger.info(f"Querying logs from {starttime_query} to {endtime_query}")
    response = client.get_log_events(
        logGroupName='Eduwatch',
        logStreamName='eni-079e15a16d6aeec9c-all',
        startTime=int(starttime_query) *1000,
        endTime=int(endtime_query) * 1000,
        startFromHead=True
    )
    logger.info(f"Successfully retrieved {len(response.get('events', []))} log events")
    
except ClientError as e:
    error_code = e.response['Error']['Code']
    error_message = e.response['Error']['Message']
    logger.error(f"AWS CloudWatch Logs API error ({error_code}): {error_message}")
    sys.exit(1)
except Exception as e:
    logger.error(f"Unexpected error during log retrieval: {e}")
    sys.exit(1)

output = {}
keys = ['version','account-id','interface-id','src_ip','dest_ip','src_port','dest_port','protocol','packets','bytes','start_time','end_time','action','log-status']

try:
    for event in response.get('events', []):
        try:
            outputlist = event['message'].split()
            if len(outputlist) != len(keys):
                logger.warning(f"Mismatched field count: expected {len(keys)}, got {len(outputlist)}")
                logger.warning(f"Message: {event['message']}")
                continue
            
            for i in range(len(outputlist)):
                output[keys[i]] = outputlist[i]
            print(json.dumps(output))
            
        except KeyError as e:
            logger.error(f"Missing expected key in event: {e}")
            logger.error(f"Event data: {event}")
        except IndexError as e:
            logger.error(f"Index error processing event: {e}")
            logger.error(f"Event message: {event.get('message', 'No message')}")
        except Exception as e:
            logger.error(f"Unexpected error processing event: {e}")
            logger.error(f"Event data: {event}")
            
    logger.info("Log processing completed successfully")
    
except Exception as e:
    logger.error(f"Unexpected error during log processing: {e}")
    sys.exit(1)


